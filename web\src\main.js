import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import axios from 'axios'
import moment from 'moment'
// 注释掉旧的socket导入，改用WebSocketManager
// import './utils/socket'
import './utils/stateManager' // 导入状态管理工具，自动清理过期备份
import { getApiBaseUrl } from './utils/serverConfig'
import { initWebSocket } from './utils/websocketManager'

Vue.config.productionTip = false

// 配置Element UI
Vue.use(ElementUI)

// 配置axios - 使用动态服务器地址
// 确保在DOM加载后执行，以便window.location可用
const configureAxios = () => {
  const baseURL = getApiBaseUrl()
  axios.defaults.baseURL = baseURL
  axios.defaults.timeout = 2000000 // 20分钟超时，适合大文件上传

  console.log('Axios baseURL configured to:', baseURL)
  console.log('Current location:', {
    hostname: window.location.hostname,
    port: window.location.port,
    protocol: window.location.protocol
  })
}

// 立即配置axios
configureAxios()

// 请求拦截器
axios.interceptors.request.use(
  config => {
    const token = store.getters['auth/token']
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axios.interceptors.response.use(
  response => {
    return response
  },
  error => {
    if (error.response) {
      const { status, data } = error.response
      if (status === 401) {
        store.dispatch('auth/logout')
        router.push('/login')
        Vue.prototype.$message.error('登录已过期，请重新登录')
      } else {
        Vue.prototype.$message.error(data.message || '请求失败')
      }
    } else {
      Vue.prototype.$message.error('网络错误')
    }
    return Promise.reject(error)
  }
)

Vue.prototype.$http = axios

// 配置moment
moment.locale('zh-cn')
Vue.prototype.$moment = moment

// 全局过滤器
Vue.filter('formatTime', function (value) {
  if (!value) return ''
  return moment(value).format('YYYY-MM-DD HH:mm:ss')
})

Vue.filter('fromNow', function (value) {
  if (!value) return ''
  return moment(value).fromNow()
})

// 创建Vue实例
const app = new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')

// 在应用启动后初始化WebSocket连接
setTimeout(async () => {
  try {
    console.log('🔧 [Main] 应用启动完成，初始化WebSocket连接...')
    await initWebSocket()

    // 将WebSocket管理器设置到store中
    const { getWebSocketManager } = await import('./utils/websocketManager')
    const wsManager = getWebSocketManager()
    store.commit('socket/SET_WEBSOCKET_MANAGER', wsManager)

    // 设置路由监听器
    setupRouteListener(wsManager)

    console.log('✅ [Main] WebSocket连接初始化完成，管理器已设置到store')
  } catch (error) {
    console.error('❌ [Main] WebSocket连接初始化失败:', error)
  }
}, 1000) // 延迟1秒，确保应用完全加载

// 设置路由监听器
function setupRouteListener(wsManager) {
  try {
    console.log('🔧 [Main] 设置路由监听器')

    // 路由开始变化时设置标识
    router.beforeEach((to, from, next) => {
      if (to.path !== from.path) {
        console.log('🔄 [Main] 检测到路由切换:', from.path, '->', to.path)
        wsManager.isRouteChanging = true

        // 在路由切换时发送特殊事件通知服务器
        if (wsManager.socket && wsManager.isConnected) {
          wsManager.socket.emit('route_changing', {
            from: from.path,
            to: to.path,
            timestamp: new Date().toISOString()
          })
        }
      }
      next()
    })

    // 路由变化完成后重置标识
    router.afterEach((to, from) => {
      setTimeout(() => {
        console.log('🔄 [Main] 路由切换完成，重置标识')
        wsManager.isRouteChanging = false

        // 路由切换完成后确保连接状态
        if (wsManager.isInitialized && (!wsManager.isConnected || !wsManager.socket || wsManager.socket.disconnected)) {
          console.log('🔄 [Main] 路由切换后检测到连接断开，重新连接')
          setTimeout(() => {
            wsManager.init()
          }, 1000)
        }
      }, 500)
    })

    console.log('✅ [Main] 路由监听器设置完成')
  } catch (error) {
    console.error('❌ [Main] 设置路由监听器失败:', error)
  }
}

// 监听token变化，重新连接WebSocket以更新认证状态
store.watch(
  (state) => state.auth.token,
  async (token) => {
    console.log('🔧 [Main] Token变化，重新初始化WebSocket连接:', !!token)
    try {
      // 导入WebSocket管理器
      const { getWebSocketManager } = await import('./utils/websocketManager')
      const wsManager = getWebSocketManager()

      // 先断开现有连接
      wsManager.disconnect()

      // 延迟重新连接
      setTimeout(async () => {
        try {
          await wsManager.init()
          // 重新设置到store中
          store.commit('socket/SET_WEBSOCKET_MANAGER', wsManager)
          console.log('✅ [Main] WebSocket重连成功，管理器已更新到store')
        } catch (error) {
          console.error('❌ [Main] WebSocket重连失败:', error)
        }
      }, 500)
    } catch (error) {
      console.error('❌ [Main] WebSocket重连过程出错:', error)
    }
  }
)
