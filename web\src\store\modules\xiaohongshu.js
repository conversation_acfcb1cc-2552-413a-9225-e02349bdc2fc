// 小红书自动化状态管理模块

// localStorage持久化工具函数
const STORAGE_KEY = 'xiaohongshu_function_states'

const loadStateFromStorage = () => {
  try {
    const saved = localStorage.getItem(STORAGE_KEY)
    if (saved) {
      const parsed = JSON.parse(saved)
      console.log('从localStorage恢复小红书状态:', parsed)
      return parsed
    }
  } catch (error) {
    console.error('从localStorage恢复状态失败:', error)
  }
  return {}
}

const saveStateToStorage = (functionStates) => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(functionStates))
    console.log('小红书状态已保存到localStorage')
  } catch (error) {
    console.error('保存状态到localStorage失败:', error)
  }
}

// 默认状态工厂函数
const createDefaultFunctionState = () => ({
  isScriptRunning: false,
  isScriptCompleted: false,
  taskId: null,
  config: {},
  selectedDevices: [],
  startTime: null,
  progress: 0,
  status: 'idle',
  logs: [],
  lastResult: null,
  // 实时状态数据
  realtimeData: {}
})

// 从localStorage恢复状态
const savedStates = loadStateFromStorage()

const state = {
  // 全局执行状态
  globalExecutionState: {
    isAnyTaskRunning: false,
    runningTasks: [], // 正在运行的任务列表
    lastUpdateTime: null
  },

  // 各功能的执行状态 - 合并默认状态和保存的状态
  functionStates: {
    profile: {
      ...createDefaultFunctionState(),
      ...(savedStates.profile || {})
    },
    searchGroupChat: {
      ...createDefaultFunctionState(),
      ...(savedStates.searchGroupChat || {})
    },
    groupMessage: {
      ...createDefaultFunctionState(),
      // 循环群发特有状态
      sentMessageCount: 0,
      processedControlCount: 0,
      executionCount: 0,
      loopCount: 0,
      currentStatus: '等待开始',
      ...(savedStates.groupMessage || {})
    },
    articleComment: {
      ...createDefaultFunctionState(),
      ...(savedStates.articleComment || {})
    },
    uidMessage: {
      ...createDefaultFunctionState(),
      ...(savedStates.uidMessage || {})
    },
    uidFileMessage: {
      ...createDefaultFunctionState(),
      ...(savedStates.uidFileMessage || {})
    },
    videoPublish: {
      ...createDefaultFunctionState(),
      ...(savedStates.videoPublish || {})
    }
  },

  // 页面状态
  pageState: {
    selectedFunction: '',
    selectedDevices: [],
    activeDeviceTab: '',
    deviceConfigs: {},
    forceBatchStopDisplay: {},
    showRealtimeStatus: false,
    executingDevices: [],
    lastVisitTime: null
  }
}

const mutations = {
  // 设置全局执行状态
  SET_GLOBAL_EXECUTION_STATE(state, { isAnyTaskRunning, runningTasks }) {
    state.globalExecutionState.isAnyTaskRunning = isAnyTaskRunning
    state.globalExecutionState.runningTasks = runningTasks || []
    state.globalExecutionState.lastUpdateTime = new Date().toISOString()
  },

  // 设置功能执行状态
  SET_FUNCTION_STATE(state, { functionType, stateData }) {
    if (state.functionStates[functionType]) {
      state.functionStates[functionType] = {
        ...state.functionStates[functionType],
        ...stateData
      }

      // 自动保存到localStorage
      saveStateToStorage(state.functionStates)
    }
  },

  // 启动任务
  START_TASK(state, { functionType, taskId, config, selectedDevices, mergeDevices = false }) {
    const functionState = state.functionStates[functionType]
    if (functionState) {
      functionState.isScriptRunning = true
      functionState.isScriptCompleted = false
      functionState.taskId = taskId
      functionState.config = config

      // 如果是合并模式，将新设备添加到现有设备列表中
      if (mergeDevices && functionState.selectedDevices) {
        const existingDevices = functionState.selectedDevices || []
        const newDevices = selectedDevices || []
        // 合并设备列表，去重
        functionState.selectedDevices = [...new Set([...existingDevices, ...newDevices])]
        console.log(`[Vuex] 合并设备列表: ${functionType}`, functionState.selectedDevices)
      } else {
        // 直接设置设备列表
        functionState.selectedDevices = selectedDevices || []
      }

      functionState.startTime = new Date().toISOString()
      functionState.status = 'running'
      functionState.progress = 0
      functionState.logs = []
    }

    // 更新全局状态
    const runningTasks = state.globalExecutionState.runningTasks.filter(t => t.functionType !== functionType)
    runningTasks.push({ functionType, taskId, startTime: new Date().toISOString() })

    state.globalExecutionState.isAnyTaskRunning = true
    state.globalExecutionState.runningTasks = runningTasks
    state.globalExecutionState.lastUpdateTime = new Date().toISOString()

    // 自动保存到localStorage
    saveStateToStorage(state.functionStates)
  },

  // 停止任务
  STOP_TASK(state, { functionType, reason = 'manual' }) {
    const functionState = state.functionStates[functionType]
    if (functionState) {
      functionState.isScriptRunning = false
      functionState.status = reason === 'completed' ? 'completed' : 'stopped'
      functionState.progress = reason === 'completed' ? 100 : functionState.progress

      if (reason === 'completed') {
        functionState.isScriptCompleted = true
        // 1分钟后自动重置完成状态
        setTimeout(() => {
          if (state.functionStates[functionType]) {
            state.functionStates[functionType].isScriptCompleted = false
          }
        }, 60000)
      }
    }

    // 更新全局状态
    const runningTasks = state.globalExecutionState.runningTasks.filter(t => t.functionType !== functionType)
    state.globalExecutionState.runningTasks = runningTasks
    state.globalExecutionState.isAnyTaskRunning = runningTasks.length > 0
    state.globalExecutionState.lastUpdateTime = new Date().toISOString()
  },

  // 更新任务进度
  UPDATE_TASK_PROGRESS(state, { functionType, progress, status, logs }) {
    const functionState = state.functionStates[functionType]
    if (functionState) {
      if (progress !== undefined) functionState.progress = progress
      if (status !== undefined) functionState.status = status
      if (logs !== undefined) {
        if (Array.isArray(logs)) {
          functionState.logs = logs
        } else {
          functionState.logs.push({
            timestamp: new Date().toISOString(),
            message: logs
          })
        }
      }
    }
  },

  // 更新循环群发特有状态
  UPDATE_GROUP_MESSAGE_STATE(state, { sentMessageCount, processedControlCount, executionCount, loopCount, currentStatus }) {
    const functionState = state.functionStates.groupMessage
    if (functionState) {
      if (sentMessageCount !== undefined) functionState.sentMessageCount = sentMessageCount
      if (processedControlCount !== undefined) functionState.processedControlCount = processedControlCount
      if (executionCount !== undefined) functionState.executionCount = executionCount
      if (loopCount !== undefined) functionState.loopCount = loopCount
      if (currentStatus !== undefined) functionState.currentStatus = currentStatus
    }
  },

  // 设置页面状态
  SET_PAGE_STATE(state, { selectedFunction, selectedDevices, activeDeviceTab, deviceConfigs, forceBatchStopDisplay, showRealtimeStatus, executingDevices }) {
    if (selectedFunction !== undefined) state.pageState.selectedFunction = selectedFunction
    if (selectedDevices !== undefined) state.pageState.selectedDevices = selectedDevices
    if (activeDeviceTab !== undefined) state.pageState.activeDeviceTab = activeDeviceTab
    if (deviceConfigs !== undefined) state.pageState.deviceConfigs = deviceConfigs
    if (forceBatchStopDisplay !== undefined) state.pageState.forceBatchStopDisplay = forceBatchStopDisplay
    if (showRealtimeStatus !== undefined) state.pageState.showRealtimeStatus = showRealtimeStatus
    if (executingDevices !== undefined) state.pageState.executingDevices = executingDevices
    state.pageState.lastVisitTime = new Date().toISOString()
  },

  // 重置功能状态
  RESET_FUNCTION_STATE(state, functionType) {
    const defaultState = createDefaultFunctionState()

    if (functionType === 'groupMessage') {
      defaultState.sentMessageCount = 0
      defaultState.processedControlCount = 0
      defaultState.executionCount = 0
      defaultState.loopCount = 0
      defaultState.currentStatus = '等待开始'
    }

    state.functionStates[functionType] = defaultState

    // 自动保存到localStorage
    saveStateToStorage(state.functionStates)
  },

  // 重置脚本执行状态（脚本执行完成时调用）
  RESET_EXECUTION_STATE(state, { deviceId, taskId, status, message }) {
    console.log('[Vuex] 重置脚本执行状态:', { deviceId, taskId, status, message })

    // 根据taskId找到对应的功能类型
    let functionType = null
    if (taskId) {
      if (taskId.includes('profile')) functionType = 'profile'
      else if (taskId.includes('searchGroupChat') || taskId.includes('group_chat')) functionType = 'searchGroupChat'
      else if (taskId.includes('groupMessage')) functionType = 'groupMessage'
      else if (taskId.includes('articleComment')) functionType = 'articleComment'
      else if (taskId.includes('uidFileMessage')) functionType = 'uidFileMessage'
      else if (taskId.includes('uidMessage')) functionType = 'uidMessage'
    }

    if (functionType && state.functionStates[functionType]) {
      // 重置执行状态
      state.functionStates[functionType].isScriptRunning = false
      state.functionStates[functionType].isScriptCompleted = true
      state.functionStates[functionType].status = status === 'success' ? 'completed' : 'failed'
      state.functionStates[functionType].progress = status === 'success' ? 100 : 0
      state.functionStates[functionType].lastResult = message

      console.log(`[Vuex] 功能 ${functionType} 执行状态已重置为: ${state.functionStates[functionType].status}`)
    }

    // 更新全局执行状态
    const runningTasks = state.globalExecutionState.runningTasks.filter(task => task.taskId !== taskId)
    state.globalExecutionState.runningTasks = runningTasks
    state.globalExecutionState.isAnyTaskRunning = runningTasks.length > 0
    state.globalExecutionState.lastUpdateTime = new Date().toISOString()
  },

  // 清理指定设备的所有状态（设备断开连接时调用）
  CLEAR_DEVICE_STATE(state, { deviceId }) {
    console.log('[小红书Vuex] 清理设备状态:', deviceId)

    // 遍历所有功能状态，清理包含该设备的执行状态和选择状态
    Object.keys(state.functionStates).forEach(functionType => {
      const functionState = state.functionStates[functionType]

      // 如果该功能的选中设备包含断开的设备，从列表中移除
      if (functionState.selectedDevices && functionState.selectedDevices.includes(deviceId)) {
        const index = functionState.selectedDevices.indexOf(deviceId)
        functionState.selectedDevices.splice(index, 1)
        console.log(`[小红书Vuex] 从功能 ${functionType} 的选中设备中移除: ${deviceId}`)
      }

      // 如果该功能正在运行且只涉及到该设备，重置功能状态
      if (functionState.isScriptRunning &&
          functionState.selectedDevices &&
          functionState.selectedDevices.length === 0) {
        console.log(`[小红书Vuex] 重置功能 ${functionType} 的执行状态（设备断开）`)
        functionState.isScriptRunning = false
        functionState.isScriptCompleted = false
        functionState.taskId = null
        functionState.config = {}
        functionState.startTime = null
        functionState.progress = 0
        functionState.status = 'idle'
        functionState.logs = []
        functionState.lastResult = null
        functionState.realtimeData = {}
      }
    })

    // 清理页面状态中的设备选择
    if (state.pageState.selectedDevices && state.pageState.selectedDevices.includes(deviceId)) {
      const index = state.pageState.selectedDevices.indexOf(deviceId)
      state.pageState.selectedDevices.splice(index, 1)
      console.log(`[小红书Vuex] 从页面状态的选中设备中移除: ${deviceId}`)
    }

    // 清理当前任务中涉及该设备的任务
    if (state.currentTasks && state.currentTasks[deviceId]) {
      delete state.currentTasks[deviceId]
      console.log(`[小红书Vuex] 清理设备 ${deviceId} 的当前任务`)
    }

    // 自动保存到localStorage
    saveStateToStorage(state.functionStates)
  },

  // 强制停止指定设备的脚本执行状态显示（设备断开连接超时时调用）
  FORCE_STOP_DEVICE_EXECUTION(state, { deviceId }) {
    console.log('[小红书Vuex] 强制停止设备执行状态显示:', deviceId)

    // 遍历所有功能状态，强制停止涉及该设备的执行状态
    Object.keys(state.functionStates).forEach(functionType => {
      const functionState = state.functionStates[functionType]

      // 如果该功能正在运行且涉及到该设备，强制停止执行状态
      if (functionState.isScriptRunning) {
        console.log(`[小红书Vuex] 强制停止功能 ${functionType} 的执行状态（设备断开连接超时）`)
        functionState.isScriptRunning = false
        functionState.isScriptCompleted = false
        functionState.taskId = null
        functionState.progress = 0
        functionState.status = 'idle'
        functionState.logs = []
        functionState.lastResult = null
        functionState.realtimeData = {}
      }
    })

    // 清理当前任务中涉及该设备的任务
    if (state.currentTasks && state.currentTasks[deviceId]) {
      delete state.currentTasks[deviceId]
      console.log(`[小红书Vuex] 清理设备 ${deviceId} 的当前任务（强制停止）`)
    }

    // 自动保存到localStorage
    saveStateToStorage(state.functionStates)
  },

  // 更新执行状态（用于处理服务器推送的状态更新）
  UPDATE_EXECUTION_STATUS(state, { taskId, deviceId, status, progress, stage, message, timestamp }) {
    console.log('[小红书Vuex] 更新执行状态:', { taskId, deviceId, status, progress, stage, message })

    // 遍历所有功能状态，找到匹配的任务并更新状态
    Object.keys(state.functionStates).forEach(functionType => {
      const functionState = state.functionStates[functionType]

      // 如果任务ID匹配或者设备ID匹配且正在执行
      if (functionState.taskId === taskId || (functionState.isScriptRunning && deviceId)) {
        console.log(`[小红书Vuex] 更新功能 ${functionType} 的执行状态`)

        if (status === 'failed' || status === 'error') {
          functionState.isScriptRunning = false
          functionState.isScriptCompleted = false
          functionState.progress = 0
          functionState.status = 'error'
          functionState.logs.push({
            timestamp: timestamp || new Date(),
            level: 'error',
            message: message || '执行失败'
          })
        } else if (status === 'completed' || status === 'success') {
          functionState.isScriptRunning = false
          functionState.isScriptCompleted = true
          functionState.progress = 100
          functionState.status = 'completed'
          functionState.logs.push({
            timestamp: timestamp || new Date(),
            level: 'success',
            message: message || '执行完成'
          })
        } else {
          functionState.progress = progress || functionState.progress
          functionState.status = status || functionState.status
          if (message) {
            functionState.logs.push({
              timestamp: timestamp || new Date(),
              level: 'info',
              message: message
            })
          }
        }
      }
    })

    // 自动保存到localStorage
    saveStateToStorage(state.functionStates)
  },

  // 重置设备执行状态（设备断开连接时调用）
  RESET_DEVICE_EXECUTION(state, { deviceId, reason }) {
    console.log('[小红书Vuex] 重置设备执行状态:', { deviceId, reason })

    // 遍历所有功能状态，重置涉及该设备的执行状态
    Object.keys(state.functionStates).forEach(functionType => {
      const functionState = state.functionStates[functionType]

      // 如果该功能正在运行，重置执行状态
      if (functionState.isScriptRunning) {
        console.log(`[小红书Vuex] 重置功能 ${functionType} 的执行状态（${reason}）`)
        functionState.isScriptRunning = false
        functionState.isScriptCompleted = false
        functionState.progress = 0
        functionState.status = 'error'
        functionState.logs.push({
          timestamp: new Date(),
          level: 'error',
          message: reason === 'device_disconnected' ? '设备断开连接，任务执行失败' : '任务执行被重置'
        })
      }
    })

    // 清理当前任务中涉及该设备的任务
    if (state.currentTasks && state.currentTasks[deviceId]) {
      delete state.currentTasks[deviceId]
      console.log(`[小红书Vuex] 清理设备 ${deviceId} 的当前任务（重置执行状态）`)
    }

    // 自动保存到localStorage
    saveStateToStorage(state.functionStates)
  }
}

const actions = {
  // 启动任务
  async startTask({ commit }, { functionType, taskId, config, selectedDevices, mergeDevices = false }) {
    console.log(`[Vuex] 启动任务: ${functionType}`, { taskId, config, selectedDevices, mergeDevices })
    commit('START_TASK', { functionType, taskId, config, selectedDevices, mergeDevices })
  },

  // 停止任务
  async stopTask({ commit }, { functionType, reason = 'manual' }) {
    console.log(`[Vuex] 停止任务: ${functionType}`, { reason })
    commit('STOP_TASK', { functionType, reason })
  },

  // 更新任务进度
  async updateTaskProgress({ commit }, { functionType, progress, status, logs }) {
    commit('UPDATE_TASK_PROGRESS', { functionType, progress, status, logs })
  },

  // 更新循环群发状态
  async updateGroupMessageState({ commit }, stateData) {
    commit('UPDATE_GROUP_MESSAGE_STATE', stateData)
  },

  // 设置页面状态
  async setPageState({ commit }, pageState) {
    commit('SET_PAGE_STATE', pageState)
  },

  // 设置功能状态
  async setFunctionState({ commit }, { functionType, stateData }) {
    commit('SET_FUNCTION_STATE', { functionType, stateData })
  },

  // 重置功能状态
  async resetFunctionState({ commit }, functionType) {
    console.log(`[Vuex] 重置功能状态: ${functionType}`)
    commit('RESET_FUNCTION_STATE', functionType)
  },

  // 检查并恢复运行中的任务
  async checkAndRestoreRunningTasks({ commit, state, rootState }) {
    console.log('[Vuex] 检查并恢复运行中的任务')

    try {
      // 调用API检查服务器端的任务状态
      const token = rootState.auth.token
      if (!token) {
        console.warn('[Vuex] 未登录，无法检查运行中任务')
        return
      }

      // 查询小红书执行日志，找出真正正在运行的任务
      const response = await fetch('/api/xiaohongshu/logs?executionStatus=running&limit=50', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.success && data.data.logs) {
        const allTasks = data.data.logs
        console.log('[Vuex] 发现所有任务:', allTasks.length, '个')

        // 过滤出真正运行中的任务（排除已停止、完成、失败的任务）
        const runningTasks = allTasks.filter(task => {
          const status = task.execution_status
          const isRunning = status === 'running' || status === 'pending'
          
          // 检查任务是否在最近5分钟内更新过
          const lastUpdate = new Date(task.started_at || task.created_at)
          const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
          const isRecent = lastUpdate > fiveMinutesAgo
          
          console.log(`[Vuex] 任务 ${task.task_id} 状态检查:`, {
            status,
            isRunning,
            lastUpdate: lastUpdate.toISOString(),
            isRecent,
            shouldRestore: isRunning && isRecent
          })
          
          return isRunning && isRecent
        })

        console.log('[Vuex] 真正运行中的任务:', runningTasks.length, '个')

        // 重置全局运行状态
        commit('SET_GLOBAL_EXECUTION_STATE', {
          isAnyTaskRunning: runningTasks.length > 0,
          runningTasks: runningTasks,
          lastUpdateTime: new Date()
        })

        // 按功能类型分组并恢复状态
        const functionGroups = {}
        runningTasks.forEach(task => {
          const functionType = task.function_type
          if (!functionGroups[functionType]) {
            functionGroups[functionType] = []
          }
          functionGroups[functionType].push(task)
        })

        // 恢复各功能的状态
        Object.keys(functionGroups).forEach(functionType => {
          const tasks = functionGroups[functionType]
          const latestTask = tasks[0] // 取最新的任务

          console.log(`[Vuex] 恢复功能 ${functionType} 的运行状态:`, latestTask)

          commit('SET_FUNCTION_STATE', {
            functionType,
            stateData: {
              isScriptRunning: true,
              isScriptCompleted: false,
              taskId: latestTask.task_id,
              config: latestTask.config_params || {},
              selectedDevices: [latestTask.device_id],
              startTime: latestTask.started_at,
              progress: latestTask.progress_percentage || 0,
              status: 'running',
              logs: [],
              lastResult: null
            }
          })
        })

        console.log('[Vuex] 任务状态恢复完成')
      } else {
        console.log('[Vuex] 没有发现运行中的任务')
        // 重置全局状态
        commit('SET_GLOBAL_EXECUTION_STATE', {
          isAnyTaskRunning: false,
          runningTasks: [],
          lastUpdateTime: new Date()
        })
      }

      console.log('[Vuex] 当前功能状态:', state.functionStates)
    } catch (error) {
      console.error('[Vuex] 检查运行中任务失败:', error)
    }
  },

  // 清理指定设备的执行状态
  clearDeviceExecutionState({ commit, state }, deviceId) {
    console.log(`[Vuex] 清理设备 ${deviceId} 的执行状态`)

    // 遍历所有功能状态，清理包含该设备的执行状态
    Object.keys(state.functionStates).forEach(functionType => {
      const functionState = state.functionStates[functionType]

      if (functionState.selectedDevices && functionState.selectedDevices.includes(deviceId)) {
        console.log(`[Vuex] 清理功能 ${functionType} 中设备 ${deviceId} 的状态`)

        // 从选中设备列表中移除该设备
        const updatedDevices = functionState.selectedDevices.filter(id => id !== deviceId)

        // 如果该功能没有其他设备在执行，重置功能状态
        if (updatedDevices.length === 0) {
          console.log(`[Vuex] 功能 ${functionType} 没有其他设备，重置功能状态`)
          commit('SET_FUNCTION_STATE', {
            functionType,
            stateData: {
              isScriptRunning: false,
              isScriptCompleted: false,
              taskId: null,
              config: {},
              selectedDevices: [],
              startTime: null,
              progress: 0,
              status: 'idle',
              logs: [],
              lastResult: null
            }
          })
        } else {
          // 更新选中设备列表
          commit('SET_FUNCTION_STATE', {
            functionType,
            stateData: {
              ...functionState,
              selectedDevices: updatedDevices
            }
          })
        }
      }
    })
  },

  // 重置全局执行状态
  resetGlobalExecutionState({ commit }) {
    console.log('[Vuex] 重置全局执行状态')
    commit('SET_GLOBAL_EXECUTION_STATE', {
      isAnyTaskRunning: false,
      runningTasks: [],
      lastUpdateTime: new Date()
    })
  },

  // 强制重置所有功能状态（用于清理错误状态）
  forceResetAllFunctionStates({ commit }) {
    console.log('[Vuex] 强制重置所有功能状态')

    const functionTypes = ['profile', 'searchGroupChat', 'groupMessage', 'articleComment', 'uidMessage', 'uidFileMessage', 'videoPublish']
    
    functionTypes.forEach(functionType => {
      commit('SET_FUNCTION_STATE', {
        functionType,
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          taskId: null,
          config: {},
          selectedDevices: [],
          startTime: null,
          progress: 0,
          status: 'idle',
          logs: [],
          lastResult: null
        }
      })
    })

    // 同时重置全局状态
    commit('SET_GLOBAL_EXECUTION_STATE', {
      isAnyTaskRunning: false,
      runningTasks: [],
      lastUpdateTime: new Date()
    })

    console.log('[Vuex] 所有功能状态已强制重置')
  }
}

const getters = {
  // 获取功能状态
  getFunctionState: (state) => (functionType) => {
    return state.functionStates[functionType] || {}
  },

  // 获取是否有任务正在运行
  isAnyTaskRunning: (state) => {
    return state.globalExecutionState.isAnyTaskRunning
  },

  // 获取正在运行的任务列表
  getRunningTasks: (state) => {
    return state.globalExecutionState.runningTasks
  },

  // 获取页面状态
  getPageState: (state) => {
    return state.pageState
  },

  // 检查特定功能是否正在运行
  isFunctionRunning: (state) => (functionType) => {
    const functionState = state.functionStates[functionType]
    return functionState ? functionState.isScriptRunning : false
  },

  // 检查特定功能是否已完成
  isFunctionCompleted: (state) => (functionType) => {
    const functionState = state.functionStates[functionType]
    return functionState ? functionState.isScriptCompleted : false
  },

  // 获取功能配置
  getFunctionConfig: (state) => (functionType) => {
    const functionState = state.functionStates[functionType]
    return functionState ? functionState.config : {}
  },

  // 获取设备的任务列表
  getDeviceTasks: (state) => (deviceId) => {
    const tasks = []
    
    // 遍历所有功能状态，查找包含该设备的运行任务
    Object.keys(state.functionStates).forEach(functionType => {
      const functionState = state.functionStates[functionType]
      
      if (functionState.isScriptRunning && 
          functionState.selectedDevices && 
          functionState.selectedDevices.includes(deviceId)) {
        tasks.push({
          functionType: functionType,
          taskId: functionState.taskId,
          status: functionState.status,
          startTime: functionState.startTime,
          progress: functionState.progress
        })
      }
    })
    
    return tasks
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
