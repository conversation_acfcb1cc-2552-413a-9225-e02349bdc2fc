<template>
  <div class="search-group-chat-config">
    <el-form :model="config" label-width="120px">
      <el-form-item label="搜索关键词">
        <el-input
          v-model="config.searchKeyword"
          placeholder="请输入搜索关键词（默认：私域）"
        />
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          系统将搜索包含此关键词的群聊
        </div>
      </el-form-item>

      <el-form-item label="目标加入次数">
        <el-input-number
          v-model="config.targetJoinCount"
          :min="1"
          :max="50"
        />
        <span style="margin-left: 10px; color: #909399;">想要成功加入的群聊数量</span>
      </el-form-item>

      <el-form-item label="最大滚动次数">
        <el-input-number
          v-model="config.maxScrollAttempts"
          :min="5"
          :max="50"
        />
        <span style="margin-left: 10px; color: #909399;">搜索群聊时最大滚动尝试次数</span>
      </el-form-item>

      <el-form-item label="小红书应用">
        <el-select
          v-model="config.selectedApp"
          placeholder="请选择要使用的小红书应用"
          @change="onAppSelectionChange"
          style="width: 100%"
        >
          <el-option
            v-for="app in xiaohongshuApps"
            :key="app.text"
            :label="app.text"
            :value="app.text"
          >
            <span style="float: left">{{ app.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ app.method === 'keyword' ? '关键词' : '正则' }}
            </span>
          </el-option>
        </el-select>
        <div style="margin-top: 5px; color: #909399; font-size: 12px;">
          选择设备上要使用的小红书应用版本
        </div>
      </el-form-item>

      <el-form-item label="详细日志">
        <el-switch
          v-model="config.enableDetailedLog"
          active-text="开启"
          inactive-text="关闭"
        />
        <span style="margin-left: 10px; color: #909399;">启用详细的执行日志输出</span>
      </el-form-item>

      <!-- 实时状态显示区域 -->
      <div class="realtime-status-section" style="margin-bottom: 20px;">
        <el-divider content-position="left">
          <span style="color: #409EFF; font-weight: bold;">实时状态</span>
        </el-divider>

        <div class="status-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
          <div class="status-item">
            <span class="status-label">已加入群聊：</span>
            <span class="status-value" style="color: #67C23A; font-weight: bold;">{{ joinedGroupCount }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">已处理步骤：</span>
            <span class="status-value" style="color: #E6A23C; font-weight: bold;">{{ processedStepCount }}</span>
          </div>

          <div class="status-item">
            <span class="status-label">滚动尝试次数：</span>
            <span class="status-value" style="color: #F56C6C; font-weight: bold;">{{ scrollAttemptCount }}</span>
          </div>
        </div>

        <div class="current-status" style="margin-bottom: 15px;">
          <span class="status-label">当前状态：</span>
          <span class="status-value" style="color: #409EFF; font-weight: bold;">{{ currentStatus || '等待开始' }}</span>
        </div>
      </div>

      <!-- 脚本控制按钮 -->
      <el-form-item label="脚本控制">
        <el-button
          type="primary"
          size="small"
          @click="startScript"
          :disabled="isScriptRunning || !canExecute"
          :loading="isScriptRunning"
        >
          {{ isScriptRunning ? '脚本执行中...' : '开始执行' }}
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click="stopScript"
          :disabled="!isScriptRunning"
          style="margin-left: 10px;"
        >
          停止脚本
        </el-button>
        <span v-if="isScriptRunning" style="margin-left: 10px; color: #67C23A; font-size: 12px;">
          脚本正在执行中...
        </span>
        <span v-else-if="isScriptCompleted" style="margin-left: 10px; color: #409EFF; font-size: 12px;">
          脚本执行完成，1分钟后可重新执行
        </span>
        <span v-else style="margin-left: 10px; color: #909399; font-size: 12px;">
          脚本未运行
        </span>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import io from 'socket.io-client'
import xiaohongshuAppSelector from '@/mixins/xiaohongshuAppSelector'

export default {
  name: 'SearchGroupChatConfig',
  mixins: [xiaohongshuAppSelector],
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    selectedDevices: {
      type: Array,
      default: () => []
    },
    onlineDevices: {
      type: Array,
      default: () => []
    },
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      updateTimer: null,
      // 保存执行时的真实logId和taskId
      currentLogId: null,
      currentTaskId: null,
      config: {
        searchKeyword: '私域',
        targetJoinCount: 5,
        maxScrollAttempts: 10,
        enableDetailedLog: false,
        selectedApp: '' // 选择的小红书应用
      },

      // 实时状态变量
      joinedGroupCount: 0,
      processedStepCount: 0,
      scrollAttemptCount: 0,
      currentStatus: '等待开始',

      // Socket连接
      socket: null
    }
  },
  computed: {
    canExecute() {
      return this.config.searchKeyword &&
             this.config.targetJoinCount > 0
    },

    // 从Vuex获取脚本运行状态
    isScriptRunning() {
      const functionState = this.$store.getters['xiaohongshu/getFunctionState']('searchGroupChat')
      return functionState ? functionState.isScriptRunning : false
    },

    // 从Vuex获取脚本完成状态
    isScriptCompleted() {
      const functionState = this.$store.getters['xiaohongshu/getFunctionState']('searchGroupChat')
      return functionState ? functionState.isScriptCompleted : false
    }
  },
  watch: {
    config: {
      handler(newConfig) {
        this.debouncedUpdate(newConfig)
      },
      deep: true,
      immediate: true
    },
    value: {
      handler(newValue) {
        if (newValue && Object.keys(newValue).length > 0) {
          this.config = { ...this.config, ...newValue }
        }
      },
      immediate: true
    }
  },
  mounted() {
    // 监听任务开始事件
    this.$root.$on('xiaohongshu-task-started', this.handleTaskStarted)

    // 监听任务停止事件
    this.$root.$on('xiaohongshu-task-stopped', this.handleTaskStopped)

    // 监听脚本完成事件
    this.$root.$on('xiaohongshu-script-completed', this.handleScriptCompleted)

    // 监听任务恢复事件
    this.$root.$on('xiaohongshu-task-restored', this.handleTaskRestored)

    // 监听设备离线事件
    this.$root.$on('device-offline', (data) => {
      if (data.deviceId === this.deviceId) {
        console.log('[SearchGroupChatConfig] 当前设备离线，重置状态')
        this.handleDeviceOffline()
      }
    })

    // 监听服务器关闭事件
    this.$root.$on('server-shutdown', (data) => {
      console.log('[SearchGroupChatConfig] 收到服务器关闭事件:', data)
      this.handleDeviceOffline() // 复用设备离线处理逻辑
    })

    // 恢复组件状态（异步）
    this.restoreComponentState().then(() => {
      console.log('[SearchGroupChatConfig] 状态恢复完成，开始初始化Socket连接')
      // 初始化Socket连接（参考循环群发的实现）
      this.initializeSocket()
    }).catch(error => {
      console.error('[SearchGroupChatConfig] 状态恢复失败:', error)
      // 即使恢复失败也要初始化Socket连接
      this.initializeSocket()
    })
  },
  beforeDestroy() {
    console.log('🔧 [SearchGroupChatConfig] 组件即将销毁，执行清理操作')

    // 保存组件状态
    this.saveComponentState()

    // 不再断开socket连接，让WebSocketManager统一管理
    console.log('🔧 [SearchGroupChatConfig] 保持WebSocket连接，仅清理事件监听')

    // 清理事件监听
    this.$root.$off('xiaohongshu-task-started', this.handleTaskStarted)
    this.$root.$off('xiaohongshu-task-stopped', this.handleTaskStopped)
    this.$root.$off('xiaohongshu-script-completed', this.handleScriptCompleted)
    this.$root.$off('xiaohongshu-task-restored', this.handleTaskRestored)
    this.$root.$off('device-offline')
    this.$root.$off('server-shutdown')

    if (this.updateTimer) {
      clearTimeout(this.updateTimer)
    }

    console.log('🔧 [SearchGroupChatConfig] 组件清理完成')
  },
  methods: {
    debouncedUpdate(config) {
      if (this.updateTimer) {
        clearTimeout(this.updateTimer)
      }
      this.updateTimer = setTimeout(() => {
        this.$emit('update', config)
      }, 300)
    },

    async startScript() {
      if (!this.canExecute) {
        this.$message.warning('请完善配置参数')
        return
      }

      try {
        console.log('[SearchGroupChatConfig] 开始执行搜索加群脚本')

        // 通过父组件的方法执行脚本
        this.$emit('execute-script', {
          functionType: 'searchGroupChat',
          config: this.config,
          deviceId: this.deviceId
        })

        console.log('[SearchGroupChatConfig] 脚本执行请求已发送')
        // 注意：不在这里更新状态，等待任务开始事件来更新状态
      } catch (error) {
        console.error('[SearchGroupChatConfig] 启动脚本失败:', error)
        this.$message.error('启动脚本失败: ' + error.message)
      }
    },

    async stopScript() {
      try {
        console.log('停止搜索群聊脚本执行')
        console.log('[SearchGroupChatConfig] 当前保存的logId:', this.currentLogId)
        console.log('[SearchGroupChatConfig] 当前保存的taskId:', this.currentTaskId)

        // 如果有设备ID，停止特定设备；否则停止所有任务
        let stopData = {}
        if (this.deviceId) {
          // 单设备停止，使用保存的真实logId和taskId
          console.log('[SearchGroupChatConfig] 停止特定设备:', this.deviceId)
          stopData = {
            deviceId: this.deviceId,
            taskId: this.currentTaskId || `xiaohongshu_searchGroupChat_${this.deviceId}`,
            logId: this.currentLogId || `xiaohongshu_searchGroupChat_${Date.now()}_${this.deviceId}`
          }
          console.log('[SearchGroupChatConfig] 停止数据:', stopData)
        } else {
          // 停止所有任务（兼容旧版本）
          console.log('[SearchGroupChatConfig] 停止所有搜索群聊任务')
        }

        // 发送停止请求到服务器
        const response = await this.$http.post('/api/xiaohongshu/stop', stopData)

        // 立即更新本地状态
        await this.$store.dispatch('xiaohongshu/setFunctionState', {
          functionType: 'searchGroupChat',
          stateData: {
            isScriptRunning: false,
            isScriptCompleted: false,
            config: this.config
          }
        })

        if (response.data.success) {
          this.$message.success('脚本停止成功')
        } else {
          this.$message.error('停止脚本失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('[SearchGroupChatConfig] 停止脚本请求失败:', error)
        this.$message.error('停止脚本请求失败')

        // 即使请求失败，也通过Vuex重置状态
        this.$store.dispatch('xiaohongshu/setFunctionState', {
          functionType: 'searchGroupChat',
          stateData: {
            isScriptRunning: false,
            isScriptCompleted: false,
            config: this.config
          }
        })
      }
    },

    // 处理任务开始事件
    handleTaskStarted(data) {
      console.log('[SearchGroupChatConfig] 收到任务开始事件:', data)
      console.log('[SearchGroupChatConfig] 当前组件deviceId:', this.deviceId)
      console.log('[SearchGroupChatConfig] 事件deviceId:', data.deviceId)
      console.log('[SearchGroupChatConfig] 功能类型匹配:', data.functionType === 'searchGroupChat')
      console.log('[SearchGroupChatConfig] 设备ID匹配:', !this.deviceId || data.deviceId === this.deviceId)

      if (data.functionType === 'searchGroupChat' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[SearchGroupChatConfig] 任务开始 (设备ID匹配):', data)
        this.isScriptRunning = true
        this.isScriptCompleted = false

        // 保存真实的logId和taskId
        if (data.logId) {
          this.currentLogId = data.logId
          console.log('[SearchGroupChatConfig] 保存logId:', this.currentLogId)
        }
        if (data.taskId) {
          this.currentTaskId = data.taskId
          console.log('[SearchGroupChatConfig] 保存taskId:', this.currentTaskId)
        }

        // 重置实时状态
        this.resetRealtimeStatus()

        // 强制更新组件
        this.$forceUpdate()
        console.log('[SearchGroupChatConfig] 组件状态已强制更新')

        this.saveComponentState()
      } else {
        console.log('[SearchGroupChatConfig] 任务开始事件不匹配，忽略')
      }
    },

    handleTaskStopped(data) {
      console.log('[SearchGroupChatConfig] 收到任务停止事件:', data)
      console.log('[SearchGroupChatConfig] 当前组件deviceId:', this.deviceId)
      console.log('[SearchGroupChatConfig] 事件deviceId:', data.deviceId)
      console.log('[SearchGroupChatConfig] 功能类型匹配:', data.functionType === 'searchGroupChat')
      console.log('[SearchGroupChatConfig] 设备ID匹配:', !this.deviceId || data.deviceId === this.deviceId)

      const functionType = typeof data === 'string' ? data : data.functionType
      const reason = data.reason || 'manual'

      // 处理批量停止或单设备停止
      const shouldStop = (functionType === 'searchGroupChat' || functionType === 'all') && (
        reason === 'batch_stop' || // 批量停止时停止所有设备
        !this.deviceId || // 没有设备ID时停止
        data.deviceId === this.deviceId // 设备ID匹配时停止
      )

      if (shouldStop) {
        console.log(`[SearchGroupChatConfig] 搜索群聊任务停止，原因: ${reason}`)
        this.isScriptRunning = false
        this.isScriptCompleted = false

        // 清空保存的logId和taskId
        this.currentLogId = null
        this.currentTaskId = null
        console.log('[SearchGroupChatConfig] 已清空logId和taskId')

        // 重置实时状态
        this.resetRealtimeStatus()

        // 强制更新组件
        this.$forceUpdate()
        console.log('[SearchGroupChatConfig] 组件状态已强制更新')

        // 更新Vuex状态
        this.$store.dispatch('xiaohongshu/stopTask', {
          functionType: 'searchGroupChat',
          reason: reason === 'batch_stop' ? 'batch_stop' : 'completed'
        })

        this.saveComponentState()

        // 如果是批量停止，显示提示信息
        if (reason === 'batch_stop') {
          this.$message.info('搜索加群功能已被批量停止')
        }
      } else {
        console.log('[SearchGroupChatConfig] 任务停止事件不匹配，忽略')
      }
    },

    // 处理脚本完成事件
    handleScriptCompleted(data) {
      console.log('[SearchGroupChatConfig] 收到脚本完成事件:', data)
      const functionType = typeof data === 'string' ? data : data.functionType
      if (functionType === 'searchGroupChat' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[SearchGroupChatConfig] 搜索群聊脚本完成')
        this.isScriptRunning = false
        this.isScriptCompleted = true

        // 清空保存的logId和taskId
        this.currentLogId = null
        this.currentTaskId = null
        console.log('[SearchGroupChatConfig] 脚本完成，已清空logId和taskId')

        // 强制更新组件
        this.$forceUpdate()
        console.log('[SearchGroupChatConfig] 脚本完成，组件状态已强制更新')

        this.saveComponentState()

        // 1分钟后重置完成状态
        setTimeout(() => {
          this.isScriptCompleted = false
          this.saveComponentState()
        }, 60000)
      }
    },

    // 处理任务恢复事件
    handleTaskRestored(data) {
      if (data.functionType === 'searchGroupChat') {
        console.log('[SearchGroupChatConfig] 恢复任务状态:', data.state)

        this.isScriptRunning = data.state.isScriptRunning
        this.isScriptCompleted = data.state.isScriptCompleted

        // 恢复配置
        if (data.state.config && Object.keys(data.state.config).length > 0) {
          this.config = { ...this.config, ...data.state.config }
        }

        console.log('[SearchGroupChatConfig] 状态已恢复:', {
          isScriptRunning: this.isScriptRunning,
          isScriptCompleted: this.isScriptCompleted,
          config: this.config
        })
      }
    },

    // 保存组件状态
    async saveComponentState() {
      try {
        await this.$store.dispatch('xiaohongshu/setFunctionState', {
          functionType: 'searchGroupChat',
          stateData: {
            isScriptRunning: this.isScriptRunning,
            isScriptCompleted: this.isScriptCompleted,
            config: this.config,
            currentLogId: this.currentLogId,
            currentTaskId: this.currentTaskId,
            // 保存实时状态数据
            realtimeData: {
              joinedGroupCount: this.joinedGroupCount,
              processedStepCount: this.processedStepCount,
              scrollAttemptCount: this.scrollAttemptCount,
              currentStatus: this.currentStatus
            }
          }
        })
        console.log('[SearchGroupChatConfig] 组件状态已保存')
      } catch (error) {
        console.error('[SearchGroupChatConfig] 保存组件状态失败:', error)
      }
    },

    // 恢复组件状态
    async restoreComponentState() {
      try {
        const functionState = this.$store.getters['xiaohongshu/getFunctionState']('searchGroupChat')

        if (functionState && Object.keys(functionState).length > 0) {
          console.log('[SearchGroupChatConfig] 恢复组件状态:', functionState)

          this.isScriptRunning = functionState.isScriptRunning || false
          this.isScriptCompleted = functionState.isScriptCompleted || false

          // 恢复logId和taskId
          this.currentLogId = functionState.currentLogId || null
          this.currentTaskId = functionState.currentTaskId || null

          // 恢复配置
          if (functionState.config && Object.keys(functionState.config).length > 0) {
            this.config = { ...this.config, ...functionState.config }
          }

          // 恢复实时状态数据
          if (functionState.realtimeData) {
            this.joinedGroupCount = functionState.realtimeData.joinedGroupCount || 0
            this.processedStepCount = functionState.realtimeData.processedStepCount || 0
            this.scrollAttemptCount = functionState.realtimeData.scrollAttemptCount || 0
            this.currentStatus = functionState.realtimeData.currentStatus || '等待开始'
            console.log('[SearchGroupChatConfig] 实时状态已恢复:', functionState.realtimeData)
          }

          console.log('[SearchGroupChatConfig] 组件状态已恢复:', {
            isScriptRunning: this.isScriptRunning,
            isScriptCompleted: this.isScriptCompleted,
            currentLogId: this.currentLogId,
            currentTaskId: this.currentTaskId,
            config: this.config,
            realtimeData: {
              joinedGroupCount: this.joinedGroupCount,
              processedStepCount: this.processedStepCount,
              scrollAttemptCount: this.scrollAttemptCount,
              currentStatus: this.currentStatus
            }
          })
        }
      } catch (error) {
        console.error('[SearchGroupChatConfig] 恢复组件状态失败:', error)
      }
    },

    // 处理实时状态更新
    handleRealtimeStatus(data) {
      console.log('🔄 [SearchGroupChatConfig] 收到实时状态数据:', data)
      console.log('📋 [SearchGroupChatConfig] 当前组件taskId:', this.currentTaskId)
      console.log('📋 [SearchGroupChatConfig] 数据中的taskId:', data.taskId)
      console.log('🔍 [SearchGroupChatConfig] taskId匹配:', this.currentTaskId && data.taskId === this.currentTaskId)

      if (this.currentTaskId && data.taskId === this.currentTaskId) {
        console.log('✅ [SearchGroupChatConfig] taskId匹配，更新实时状态:', data)

        // 更新统计数据
        if (data.joinedGroupCount !== undefined) {
          this.joinedGroupCount = data.joinedGroupCount
          console.log('📊 [SearchGroupChatConfig] 更新已加入群聊数:', this.joinedGroupCount)
        }
        if (data.processedStepCount !== undefined) {
          this.processedStepCount = data.processedStepCount
          console.log('📊 [SearchGroupChatConfig] 更新已处理步骤数:', this.processedStepCount)
        }
        if (data.scrollAttemptCount !== undefined) {
          this.scrollAttemptCount = data.scrollAttemptCount
          console.log('📊 [SearchGroupChatConfig] 更新滚动尝试次数:', this.scrollAttemptCount)
        }
        if (data.currentStatus) {
          this.currentStatus = data.currentStatus
          console.log('📊 [SearchGroupChatConfig] 更新当前状态:', this.currentStatus)
        }

        console.log('✅ [SearchGroupChatConfig] 实时状态已更新:', {
          joinedGroupCount: this.joinedGroupCount,
          processedStepCount: this.processedStepCount,
          scrollAttemptCount: this.scrollAttemptCount,
          currentStatus: this.currentStatus
        })

        // 强制更新视图
        this.$forceUpdate()
        console.log('🔄 [SearchGroupChatConfig] 已强制更新视图')
      } else {
        console.log('❌ [SearchGroupChatConfig] taskId不匹配或currentTaskId为空，忽略实时状态更新')
      }
    },

    // 重置实时状态
    resetRealtimeStatus() {
      this.joinedGroupCount = 0
      this.processedStepCount = 0
      this.scrollAttemptCount = 0
      this.currentStatus = '等待开始'
      console.log('[SearchGroupChatConfig] 实时状态已重置')
    },

    // 初始化Socket连接 - 使用统一的WebSocket管理器
    async initializeSocket() {
      try {
        console.log('🔧 [SearchGroupChatConfig] 使用统一WebSocket管理器')
        // 使用全局WebSocket管理器，不创建独立连接
        const { getWebSocketManager } = await import('@/utils/websocketManager')
        this.wsManager = getWebSocketManager()

        // 确保连接
        await this.wsManager.init()

        // 获取socket实例用于事件监听
        this.socket = this.wsManager.socket

        console.log('✅ [SearchGroupChatConfig] 已连接到统一WebSocket管理器')
      } catch (error) {
        console.error('❌ [SearchGroupChatConfig] WebSocket连接失败:', error)
      }

      this.socket.on('connect', () => {
        console.log('✅ [SearchGroupChatConfig] Socket连接成功')
        // 不需要重复注册，主WebSocket管理器已经处理了用户认证
        // this.socket.emit('web_client_connect', { userId: 'search_group_chat_config' })
      })

      this.socket.on('disconnect', () => {
        console.log('❌ [SearchGroupChatConfig] Socket连接断开')
      })

      this.socket.on('connect_error', (error) => {
        console.error('❌ [SearchGroupChatConfig] Socket连接错误:', error)
      })

      // 监听所有事件进行调试
      this.socket.onAny((eventName, data) => {
        if (eventName.includes('xiaohongshu') || eventName.includes('test')) {
          console.log(`🔍 [SearchGroupChatConfig] 收到WebSocket事件: ${eventName}`, data)
        }
      })

      // 监听实时状态更新
      this.socket.on('xiaohongshu_realtime_status', (data) => {
        console.log('🎯 [SearchGroupChatConfig] 收到WebSocket实时状态事件:', data)
        this.handleRealtimeStatus(data)
      })

      // 监听脚本执行完成事件
      this.socket.on('xiaohongshu_execution_completed', (data) => {
        console.log('[SearchGroupChatConfig] 收到WebSocket脚本执行完成事件:', data)
        if (data.deviceId === this.deviceId || !this.deviceId) {
          console.log('[SearchGroupChatConfig] 脚本执行完成，更新状态')

          // 通过Vuex更新状态
          this.$store.dispatch('xiaohongshu/setFunctionState', {
            functionType: 'searchGroupChat',
            stateData: {
              isScriptRunning: false,
              isScriptCompleted: data.status === 'success',
              config: this.config
            }
          })

          // 如果执行成功，1分钟后重置完成状态
          if (data.status === 'success') {
            setTimeout(() => {
              this.$store.dispatch('xiaohongshu/setFunctionState', {
                functionType: 'searchGroupChat',
                stateData: {
                  isScriptRunning: false,
                  isScriptCompleted: false,
                  config: this.config
                }
              })
            }, 60000)
          }
        }
      })

      // 监听测试广播事件
      this.socket.on('test_realtime_broadcast', (data) => {
        console.log('🧪 [SearchGroupChatConfig] 收到测试广播:', data)
      })

      console.log('✅ [SearchGroupChatConfig] Socket初始化完成')
    },

    // 处理设备离线事件
    handleDeviceOffline() {
      console.log('[SearchGroupChatConfig] 处理设备离线，重置状态')

      // 重置脚本执行状态
      this.$store.dispatch('xiaohongshu/setFunctionState', {
        functionType: 'searchGroupChat',
        stateData: {
          isScriptRunning: false,
          isScriptCompleted: false,
          config: this.config
        }
      })

      // 重置实时状态
      this.resetRealtimeStatus()

      // 清除任务ID
      this.currentTaskId = null
      this.currentLogId = null

      // 保存状态
      this.saveComponentState()

      console.log('[SearchGroupChatConfig] 设备离线处理完成')
    }
  }
}
</script>

<style scoped>
.search-group-chat-config {
  padding: 20px;
}

.el-form-item {
  margin-bottom: 20px;
}

.el-button {
  margin-right: 10px;
}
</style>
